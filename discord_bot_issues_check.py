#!/usr/bin/env python3
"""
Discord Bot Issues Check
Kiểm tra các vấn đề tiềm ẩn trong Discord bot
"""

import asyncio
import logging
import time
from mexc_client import MEXCClient

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class DiscordBotIssuesCheck:
    def __init__(self):
        self.issues = []
        
    async def check_api_consistency(self):
        """Check API data consistency"""
        print("\n🔍 Checking API data consistency...")
        
        async with MEXCClient() as client:
            # Get data multiple times and check consistency
            results = []
            for i in range(3):
                price_data = await client.get_price_data("PAXG_USDT")
                balance_data = await client.get_futures_account_assets()
                positions_data = await client.get_futures_positions()
                orders_data = await client.get_futures_open_orders()
                
                results.append({
                    'price': price_data,
                    'balance': balance_data,
                    'positions': positions_data,
                    'orders': orders_data
                })
                
                await asyncio.sleep(2)  # Wait between calls
            
            # Check for inconsistencies
            for i in range(1, len(results)):
                prev = results[i-1]
                curr = results[i]
                
                # Balance should be consistent (unless there are trades)
                if prev['balance'] and curr['balance']:
                    prev_usdt = next((x for x in prev['balance'] if x['currency'] == 'USDT'), None)
                    curr_usdt = next((x for x in curr['balance'] if x['currency'] == 'USDT'), None)
                    
                    if prev_usdt and curr_usdt:
                        prev_equity = prev_usdt.get('equity', 0)
                        curr_equity = curr_usdt.get('equity', 0)
                        
                        if abs(prev_equity - curr_equity) > 0.01:  # More than 1 cent difference
                            self.issues.append(f"Balance inconsistency: {prev_equity} -> {curr_equity}")
                
                # Positions count should be consistent
                if len(prev['positions']) != len(curr['positions']):
                    self.issues.append(f"Position count changed: {len(prev['positions'])} -> {len(curr['positions'])}")
            
            print(f"✅ API consistency check completed")
    
    async def check_error_handling_edge_cases(self):
        """Check error handling for edge cases"""
        print("\n🛡️ Checking error handling edge cases...")
        
        async with MEXCClient() as client:
            # Test with various invalid inputs
            test_cases = [
                ("", "Empty symbol"),
                ("INVALID_SYMBOL_VERY_LONG_NAME", "Very long symbol"),
                ("BTC/USDT", "Symbol with slash"),
                ("btc_usdt", "Lowercase symbol"),
                ("BTC-USDT", "Symbol with dash"),
                (None, "None symbol")
            ]
            
            for symbol, description in test_cases:
                try:
                    result = await client.get_price_data(symbol)
                    if result is not None:
                        self.issues.append(f"Unexpected success for {description}: {symbol}")
                except Exception as e:
                    # This is expected, but check if it's handled gracefully
                    if "Traceback" in str(e):
                        self.issues.append(f"Unhandled exception for {description}: {e}")
            
            print("✅ Error handling edge cases checked")
    
    async def check_data_format_consistency(self):
        """Check data format consistency"""
        print("\n📊 Checking data format consistency...")
        
        async with MEXCClient() as client:
            price_data = await client.get_price_data("PAXG_USDT")
            
            if price_data:
                # Check required fields
                required_fields = ['current_price', 'price_change_24h', 'price_change_percent_24h', 'volume']
                for field in required_fields:
                    if field not in price_data:
                        self.issues.append(f"Missing required field in price data: {field}")
                
                # Check data types
                numeric_fields = ['current_price', 'price_change_24h', 'price_change_percent_24h', 'volume']
                for field in numeric_fields:
                    if field in price_data:
                        try:
                            float(price_data[field])
                        except (ValueError, TypeError):
                            self.issues.append(f"Non-numeric value in {field}: {price_data[field]}")
            
            # Check balance data format
            balance_data = await client.get_futures_account_assets()
            if balance_data:
                for asset in balance_data:
                    if 'currency' not in asset:
                        self.issues.append("Missing 'currency' field in balance data")
                    if 'equity' not in asset:
                        self.issues.append("Missing 'equity' field in balance data")
            
            print("✅ Data format consistency checked")
    
    async def check_timestamp_issues(self):
        """Check for timestamp-related issues"""
        print("\n⏰ Checking timestamp issues...")
        
        async with MEXCClient() as client:
            # Check if timestamps are reasonable
            start_time = time.time() * 1000  # Convert to milliseconds
            
            # Make API calls and check response times
            price_data = await client.get_price_data("PAXG_USDT")
            
            end_time = time.time() * 1000
            
            # Check if the call took too long (might indicate timeout issues)
            call_duration = end_time - start_time
            if call_duration > 30000:  # More than 30 seconds
                self.issues.append(f"API call took too long: {call_duration/1000:.2f} seconds")
            
            # Check if we're getting timestamp-related errors
            # This would be caught in the API response parsing
            
            print("✅ Timestamp issues checked")
    
    async def check_concurrent_access_safety(self):
        """Check concurrent access safety"""
        print("\n🔄 Checking concurrent access safety...")
        
        # Test multiple clients accessing simultaneously
        clients = []
        tasks = []
        
        try:
            # Create multiple clients
            for i in range(3):
                client = MEXCClient()
                await client.__aenter__()
                clients.append(client)
            
            # Make concurrent calls
            for client in clients:
                tasks.append(client.get_price_data("PAXG_USDT"))
                tasks.append(client.get_futures_account_assets())
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Check for exceptions
            exception_count = sum(1 for r in results if isinstance(r, Exception))
            if exception_count > 0:
                self.issues.append(f"Concurrent access caused {exception_count} exceptions")
            
        finally:
            # Cleanup
            for client in clients:
                await client.__aexit__(None, None, None)
        
        print("✅ Concurrent access safety checked")
    
    async def check_memory_and_resource_leaks(self):
        """Check for memory and resource leaks"""
        print("\n🧠 Checking memory and resource leaks...")
        
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        initial_connections = len(process.connections())
        
        # Create and destroy many clients to test for leaks
        for i in range(5):
            async with MEXCClient() as client:
                await client.get_price_data("PAXG_USDT")
                await client.get_futures_account_assets()
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        final_connections = len(process.connections())
        
        memory_increase = final_memory - initial_memory
        connection_increase = final_connections - initial_connections
        
        if memory_increase > 10:  # More than 10MB increase
            self.issues.append(f"Potential memory leak: {memory_increase:.1f}MB increase")
        
        if connection_increase > 0:
            self.issues.append(f"Connection leak: {connection_increase} connections not closed")
        
        print(f"✅ Memory: {initial_memory:.1f}MB -> {final_memory:.1f}MB (+{memory_increase:.1f}MB)")
        print(f"✅ Connections: {initial_connections} -> {final_connections} (+{connection_increase})")
    
    async def run_all_checks(self):
        """Run all checks"""
        print("🚀 Starting Discord bot issues check...\n")
        
        checks = [
            self.check_api_consistency,
            self.check_error_handling_edge_cases,
            self.check_data_format_consistency,
            self.check_timestamp_issues,
            self.check_concurrent_access_safety,
            self.check_memory_and_resource_leaks
        ]
        
        for check in checks:
            try:
                await check()
            except Exception as e:
                self.issues.append(f"Check {check.__name__} failed: {e}")
                print(f"❌ {check.__name__} failed: {e}")
        
        # Summary
        print(f"\n📊 Issues Check Summary:")
        print(f"Total issues found: {len(self.issues)}")
        
        if self.issues:
            print("\n🚨 Issues found:")
            for i, issue in enumerate(self.issues, 1):
                print(f"{i}. {issue}")
        else:
            print("✅ No issues found!")

async def main():
    checker = DiscordBotIssuesCheck()
    await checker.run_all_checks()

if __name__ == "__main__":
    asyncio.run(main())
