#!/usr/bin/env python3
"""
Comprehensive API and Synchronization Test
Ki<PERSON>m tra toàn diện các vấn đề API và đồng bộ
"""

import asyncio
import logging
import time
import concurrent.futures
from mexc_client import MEXCClient
import aiohttp

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class ComprehensiveAPITest:
    def __init__(self):
        self.results = {}
        self.errors = []
        
    async def test_concurrent_api_calls(self):
        """Test concurrent API calls for race conditions"""
        print("\n🔄 Testing concurrent API calls...")
        
        async def make_concurrent_calls():
            async with MEXCClient() as client:
                tasks = []
                # Create multiple concurrent calls
                for i in range(5):
                    tasks.append(client.get_price_data("PAXG_USDT"))
                    tasks.append(client.get_futures_account_assets())
                    tasks.append(client.get_futures_positions())
                    tasks.append(client.get_futures_open_orders())
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                return results
        
        try:
            results = await make_concurrent_calls()
            success_count = sum(1 for r in results if not isinstance(r, Exception))
            error_count = len(results) - success_count
            
            print(f"✅ Concurrent calls: {success_count} success, {error_count} errors")
            
            # Check for exceptions
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    self.errors.append(f"Concurrent call {i}: {result}")
                    
        except Exception as e:
            self.errors.append(f"Concurrent test failed: {e}")
            print(f"❌ Concurrent test failed: {e}")
    
    async def test_session_management(self):
        """Test session creation and cleanup"""
        print("\n🔧 Testing session management...")
        
        try:
            # Test multiple session creations
            clients = []
            for i in range(3):
                client = MEXCClient()
                await client.__aenter__()
                clients.append(client)
                
            # Test API calls with multiple sessions
            tasks = []
            for client in clients:
                tasks.append(client.get_price_data("PAXG_USDT"))
                
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Cleanup sessions
            for client in clients:
                await client.__aexit__(None, None, None)
                
            success_count = sum(1 for r in results if not isinstance(r, Exception))
            print(f"✅ Session management: {success_count}/{len(results)} sessions worked")
            
        except Exception as e:
            self.errors.append(f"Session management test failed: {e}")
            print(f"❌ Session management test failed: {e}")
    
    async def test_retry_mechanism(self):
        """Test retry mechanism with simulated failures"""
        print("\n🔄 Testing retry mechanism...")
        
        async with MEXCClient() as client:
            # Test with invalid symbol to trigger retries
            start_time = time.time()
            result = await client.get_price_data("INVALID_SYMBOL")
            end_time = time.time()
            
            duration = end_time - start_time
            print(f"✅ Retry test completed in {duration:.2f}s, result: {result is not None}")
            
            if duration < 3:  # Should take at least 3 seconds with retries
                self.errors.append("Retry mechanism may not be working properly")
    
    async def test_timeout_handling(self):
        """Test timeout handling"""
        print("\n⏱️ Testing timeout handling...")
        
        try:
            # Create a client with very short timeout
            timeout = aiohttp.ClientTimeout(total=1)  # 1 second timeout
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                client = MEXCClient()
                client.session = session
                
                # This should timeout quickly
                start_time = time.time()
                result = await client.get_price_data("PAXG_USDT")
                end_time = time.time()
                
                duration = end_time - start_time
                print(f"✅ Timeout test: {duration:.2f}s, result: {result is not None}")
                
        except Exception as e:
            print(f"✅ Timeout handled correctly: {type(e).__name__}")
    
    async def test_memory_leaks(self):
        """Test for potential memory leaks"""
        print("\n🧠 Testing for memory leaks...")
        
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Create and destroy many clients
        for i in range(10):
            async with MEXCClient() as client:
                await client.get_price_data("PAXG_USDT")
                
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        print(f"✅ Memory test: {initial_memory:.1f}MB -> {final_memory:.1f}MB (+{memory_increase:.1f}MB)")
        
        if memory_increase > 50:  # More than 50MB increase
            self.errors.append(f"Potential memory leak: {memory_increase:.1f}MB increase")
    
    async def test_api_rate_limits(self):
        """Test API rate limiting"""
        print("\n🚦 Testing API rate limits...")
        
        async with MEXCClient() as client:
            start_time = time.time()
            
            # Make rapid API calls
            tasks = []
            for i in range(10):
                tasks.append(client.get_price_data("PAXG_USDT"))
                
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            end_time = time.time()
            duration = end_time - start_time
            
            success_count = sum(1 for r in results if not isinstance(r, Exception))
            print(f"✅ Rate limit test: {success_count}/10 calls in {duration:.2f}s")
            
            # Check if calls were properly spaced (should have random delays)
            if duration < 10:  # Should take at least 10 seconds with random delays
                self.errors.append("Rate limiting may not be working properly")
    
    async def test_error_handling_consistency(self):
        """Test consistent error handling across all methods"""
        print("\n🛡️ Testing error handling consistency...")
        
        async with MEXCClient() as client:
            # Test with missing credentials
            original_key = client.api_key
            original_secret = client.api_secret
            
            client.api_key = None
            client.api_secret = None
            
            # Test all private endpoints
            methods = [
                client.get_futures_account_assets,
                client.get_futures_positions,
                client.get_futures_open_orders
            ]
            
            for method in methods:
                try:
                    result = await method()
                    if result != []:  # Should return empty list for missing credentials
                        self.errors.append(f"{method.__name__} doesn't handle missing credentials properly")
                except Exception as e:
                    self.errors.append(f"{method.__name__} threw exception with missing credentials: {e}")
            
            # Restore credentials
            client.api_key = original_key
            client.api_secret = original_secret
            
            print("✅ Error handling consistency tested")
    
    async def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting comprehensive API and synchronization tests...\n")
        
        tests = [
            self.test_concurrent_api_calls,
            self.test_session_management,
            self.test_retry_mechanism,
            self.test_timeout_handling,
            self.test_memory_leaks,
            self.test_api_rate_limits,
            self.test_error_handling_consistency
        ]
        
        for test in tests:
            try:
                await test()
            except Exception as e:
                self.errors.append(f"Test {test.__name__} failed: {e}")
                print(f"❌ {test.__name__} failed: {e}")
        
        # Summary
        print(f"\n📊 Test Summary:")
        print(f"Total errors found: {len(self.errors)}")
        
        if self.errors:
            print("\n🚨 Issues found:")
            for i, error in enumerate(self.errors, 1):
                print(f"{i}. {error}")
        else:
            print("✅ No issues found!")

async def main():
    tester = ComprehensiveAPITest()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
