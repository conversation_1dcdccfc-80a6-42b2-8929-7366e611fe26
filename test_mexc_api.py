#!/usr/bin/env python3
"""
Test script to verify MEXC API credentials and endpoints
"""

import asyncio
import aiohttp
import hashlib
import hmac
import time
import yaml
import json

class MEXCAPITester:
    def __init__(self):
        self.futures_base_url = "https://contract.mexc.com"
        self.api_key = None
        self.api_secret = None
        self.load_config()

    def load_config(self):
        """Load trading configuration from config.ymal"""
        try:
            with open('config.ymal', 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
                mexc_config = config.get('mexc', {})
                self.api_key = mexc_config.get('api_key')
                self.api_secret = mexc_config.get('api_secret')
                print(f"Config loaded - API Key: {'***' + self.api_key[-4:] if self.api_key else 'None'}")
                print(f"Config loaded - API Secret: {'***' + self.api_secret[-4:] if self.api_secret else 'None'}")
        except Exception as e:
            print(f"Error loading config: {e}")

    def _generate_signature(self, query_string: str = None) -> str:
        """Generate signature for futures API requests"""
        if not self.api_secret:
            return ""

        timestamp = str(int(time.time() * 1000))
        # For MEXC Futures API: accessKey + timestamp + requestParam
        message = self.api_key + timestamp
        if query_string:
            message += query_string
        else:
            message += ""  # Empty string if no params

        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        return signature

    def _get_headers(self, signed: bool = False) -> dict:
        """Get request headers for futures API"""
        timestamp = str(int(time.time() * 1000))
        headers = {
            "ApiKey": self.api_key,
            "Request-Time": timestamp,
            "Content-Type": "application/json"
        }
        return headers

    async def test_public_endpoint(self):
        """Test public price endpoint"""
        print("\n=== Testing Public Price Endpoint ===")
        async with aiohttp.ClientSession() as session:
            url = f"{self.futures_base_url}/api/v1/contract/ticker"
            params = {"symbol": "PAXG_USDT"}
            
            async with session.get(url, params=params) as response:
                response_text = await response.text()
                print(f"Status: {response.status}")
                print(f"Response: {response_text}")
                
                if response.status == 200:
                    try:
                        data = json.loads(response_text)
                        if data.get('success'):
                            print("✅ Public endpoint working correctly")
                        else:
                            print("❌ Public endpoint returned error")
                    except:
                        print("❌ Failed to parse response")
                else:
                    print("❌ Public endpoint failed")

    async def test_private_endpoint(self):
        """Test private account endpoint"""
        print("\n=== Testing Private Account Endpoint ===")
        if not self.api_key or not self.api_secret:
            print("❌ API credentials missing")
            return

        async with aiohttp.ClientSession() as session:
            url = f"{self.futures_base_url}/api/v1/private/account/assets"
            signature = self._generate_signature()
            headers = self._get_headers()
            headers["Signature"] = signature

            print(f"URL: {url}")
            print(f"Headers: {headers}")
            print(f"Signature: {signature}")

            async with session.get(url, headers=headers) as response:
                response_text = await response.text()
                print(f"Status: {response.status}")
                print(f"Response: {response_text}")
                
                if response.status == 200:
                    try:
                        data = json.loads(response_text)
                        if data.get('success'):
                            print("✅ Private endpoint working correctly")
                            print(f"Account data: {data.get('data', [])}")
                        else:
                            print(f"❌ Private endpoint returned error: {data.get('msg', 'Unknown error')}")
                    except:
                        print("❌ Failed to parse response")
                else:
                    print("❌ Private endpoint failed")

    async def test_positions_endpoint(self):
        """Test positions endpoint"""
        print("\n=== Testing Positions Endpoint ===")
        if not self.api_key or not self.api_secret:
            print("❌ API credentials missing")
            return

        async with aiohttp.ClientSession() as session:
            url = f"{self.futures_base_url}/api/v1/private/position/open_positions"
            signature = self._generate_signature()
            headers = self._get_headers()
            headers["Signature"] = signature

            print(f"URL: {url}")
            print(f"Headers: {headers}")

            async with session.get(url, headers=headers) as response:
                response_text = await response.text()
                print(f"Status: {response.status}")
                print(f"Response: {response_text}")
                
                if response.status == 200:
                    try:
                        data = json.loads(response_text)
                        if data.get('success'):
                            print("✅ Positions endpoint working correctly")
                            print(f"Positions data: {data.get('data', [])}")
                        else:
                            print(f"❌ Positions endpoint returned error: {data.get('msg', 'Unknown error')}")
                    except:
                        print("❌ Failed to parse response")
                else:
                    print("❌ Positions endpoint failed")

async def main():
    tester = MEXCAPITester()
    await tester.test_public_endpoint()
    await tester.test_private_endpoint()
    await tester.test_positions_endpoint()

if __name__ == "__main__":
    asyncio.run(main()) 