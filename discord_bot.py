import discord
from discord.ext import commands, tasks
import asyncio
import yaml
import logging
from datetime import datetime
from typing import Optional
from mexc_client import MEXCClient

logging.basicConfig(level=logging.INFO)

class PAXGBot(commands.Bot):
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = False
        super().__init__(command_prefix='!', intents=intents)

        self.config = self.load_config()
        self.paxg_channel = None
        self.pinned_message = None
        self.last_prices = {}  # Track prices for multiple symbols
        self.last_volumes = {}  # Track volumes for multiple symbols
        self.price_threshold = 0.5  # 0.5% thay đổi giá
        self.volume_threshold = 20  # 20% thay đổi volume

        # Position persistence cache to handle API failures
        self.last_positions_cache = []
        self.last_balance_cache = None
        self.last_successful_update = None

        # Trading pairs configuration - only PAXG for price monitoring (Futures format)
        self.trading_pairs = {
            "PAXG_USDT": {"name": "PAXG", "icon": "🧈"}
        }
        
    def load_config(self):
        with open('config.ymal', 'r', encoding='utf-8') as file:
            return yaml.safe_load(file)

    def calculate_fallback_pnl(self, position, current_price=None):
        """Calculate PnL when Balance API fails"""
        try:
            if not current_price:
                # Use cached price or estimate
                current_price = self.last_prices.get("PAXG_USDT", {}).get("current_price", 0)

            if current_price <= 0:
                return 0.0

            entry_price = float(position.get('holdAvgPrice', 0))
            hold_vol = float(position.get('holdVol', 0))
            position_type = position.get('positionType', 1)  # 1=LONG, 2=SHORT

            if entry_price <= 0 or hold_vol <= 0:
                return 0.0

            # Calculate PnL based on position type
            if position_type == 2:  # SHORT
                pnl = (entry_price - current_price) * (hold_vol / 1000.0)  # Convert to PAXG units
            else:  # LONG
                pnl = (current_price - entry_price) * (hold_vol / 1000.0)

            return pnl
        except Exception as e:
            logging.error(f"Error calculating fallback PnL: {e}")
            return 0.0
    
    async def on_ready(self):
        print(f'{self.user} đã kết nối thành công!')

        guild = self.get_guild(int(self.config['discord']['guild_id']))
        print(f"Guild found: {guild}")
        if guild:
            self.paxg_channel = discord.utils.get(guild.channels, name='paxg')
            print(f"PAXG channel found: {self.paxg_channel}")
            if not self.paxg_channel:
                print("Không tìm thấy channel 'paxg'. Vui lòng tạo channel này.")
                return
        else:
            print("Không tìm thấy guild!")
            return

        print("Starting price monitor...")
        self.price_monitor.start()
        print("Sending initial dashboard...")
        await self.send_initial_dashboard()
    
    async def send_initial_dashboard(self):
        async with MEXCClient() as client:
            combined_embed = await self.create_combined_dashboard()
            if combined_embed:
                message = await self.paxg_channel.send(embed=combined_embed)
                await message.pin()
                self.pinned_message = message

                # Initialize last prices and volumes for all trading pairs
                for symbol in self.trading_pairs.keys():
                    price_data = await client.get_price_data(symbol)
                    if price_data:
                        self.last_prices[symbol] = price_data['current_price']
                        self.last_volumes[symbol] = price_data['volume']
    
    def create_price_embed(self, data, is_initial=False, alert_type=None, symbol="PAXG_USDT"):
        current_price = data['current_price']
        price_change_24h = data['price_change_24h']
        price_change_percent_24h = data['price_change_percent_24h']

        # Get symbol config
        config = self.trading_pairs.get(symbol, {"name": "CRYPTO", "icon": "📊"})
        symbol_name = config["name"]
        symbol_icon = config["icon"]

        color = discord.Color.green() if price_change_24h >= 0 else discord.Color.red()

        if alert_type:
            if alert_type == "price":
                title = f"🚨 CẢNH BÁO GIÁ {symbol_name}"
            elif alert_type == "volume":
                title = f"🚨 CẢNH BÁO VOLUME {symbol_name}"
            else:
                title = f"🚨 CẢNH BÁO {symbol_name}"
        elif is_initial:
            title = f"📊 CẬP NHẬT GIÁ {symbol_name}"
        else:
            title = f"📊 CẬP NHẬT GIÁ {symbol_name}"

        embed = discord.Embed(title=title, color=color, timestamp=datetime.now())

        change_24h_icon = "🔴" if price_change_24h < 0 else "🟢"

        content = f"""```
{symbol_icon} Giá hiện tại      ${current_price:>8,.2f}
{change_24h_icon} Thay đổi 24h      ${abs(price_change_24h):>8,.2f}
📊 Cao nhất 24h      ${data['high_price']:>8,.2f}
📊 Thấp nhất 24h     ${data['low_price']:>8,.2f}
💎 Volume 24h      {data['volume']:>8,.2f} {symbol_name}
```"""

        embed.description = content
        embed.set_footer(text="MEXC Exchange • Cập nhật mỗi 60 giây")

        return embed

    async def create_combined_dashboard(self) -> Optional[discord.Embed]:
        async with MEXCClient() as client:
            # Get price data for all trading pairs
            all_price_data = {}
            for symbol, config in self.trading_pairs.items():
                price_data = await client.get_price_data(symbol)
                if price_data:
                    all_price_data[symbol] = price_data

            if not all_price_data:
                logging.error("Failed to get price data for any trading pairs")
                return None

            # Get futures trading data with fallback to cache
            futures_positions = await client.get_futures_positions()
            futures_balance = await client.get_futures_account_assets()
            futures_open_orders = await client.get_futures_open_orders()

            # Cache successful data
            if futures_positions is not None:
                self.last_positions_cache = futures_positions
            if futures_balance is not None:
                self.last_balance_cache = futures_balance

            # Use cache if current call failed
            if futures_positions is None and self.last_positions_cache:
                futures_positions = self.last_positions_cache
                logging.warning("Using cached positions data due to API failure")

            if futures_balance is None and self.last_balance_cache:
                futures_balance = self.last_balance_cache
                logging.warning("Using cached balance data due to API failure")

            # Use PAXG for embed color (primary pair)
            paxg_data = all_price_data.get("PAXG_USDT")
            if paxg_data:
                price_change_24h = paxg_data['price_change_24h']
                color = discord.Color.green() if price_change_24h >= 0 else discord.Color.red()
            else:
                color = discord.Color.blue()

            embed = discord.Embed(
                title="📊 CẬP NHẬT GIÁ PAXG",
                color=color,
                timestamp=datetime.now()
            )

            # PAXG Price Section (removed CHG DAY and day open price)
            paxg_data = all_price_data.get("PAXG_USDT")
            if paxg_data:
                current_price = paxg_data['current_price']
                price_change_24h = paxg_data['price_change_24h']

                change_24h_icon = "🔴" if price_change_24h < 0 else "🟢"

                price_content = f"""```
🧈 Giá hiện tại      ${current_price:>8,.2f}
{change_24h_icon} Thay đổi 24h      ${abs(price_change_24h):>8,.2f}
📊 Cao nhất 24h      ${paxg_data['high_price']:>8,.2f}
📊 Thấp nhất 24h     ${paxg_data['low_price']:>8,.2f}
💎 Volume 24h      {paxg_data['volume']:>8,.2f} PAXG
```

"""
            else:
                price_content = "```\n❌ Không thể lấy dữ liệu PAXG\n```\n\n"

            # Trading Status Dashboard Section
            trading_content = "📊 **Trading Status Dashboard**\n"

            # Account Status - Futures only
            asset_value = 0.0
            wallet_balance = 0.0

            if futures_balance:
                # Calculate futures balance
                for asset in futures_balance:
                    if asset.get('currency') == 'USDT':
                        equity = float(asset.get('equity', 0))
                        unrealized_pnl = float(asset.get('unrealized', 0))

                        asset_value = equity  # Giá trị tài sản (bao gồm P&L)
                        wallet_balance = equity - unrealized_pnl  # Số dư ví (không bao gồm P&L)
                        break

                trading_content += f"""```
💰 Account Status (Futures)
Giá trị tài sản:    ${asset_value:>8,.2f}
Số dư ví:           ${wallet_balance:>8,.2f}
```"""
            else:
                trading_content += "```\n❌ Futures account data not available\nAPI credentials may be missing or invalid\n```"

            # P&L Summary - Use real futures data
            total_unrealized_pnl = 0.0
            active_positions_count = 0
            new_orders_count = 0

            # Count actual open positions (holdVol > 0)
            if futures_positions:
                for pos in futures_positions:
                    hold_vol = float(pos.get('holdVol', 0))
                    if hold_vol > 0:
                        active_positions_count += 1

            # Count only new orders (not TP/SL orders)
            if futures_open_orders:
                for order in futures_open_orders:
                    position_id = order.get('positionId', 0)
                    # Only count orders that don't belong to any position (new orders)
                    if position_id == 0:
                        new_orders_count += 1

            # Calculate total unrealized P&L with fallback
            if futures_balance:
                for asset in futures_balance:
                    unrealized = float(asset.get('unrealized', 0))
                    total_unrealized_pnl += unrealized
            elif futures_positions:
                # Fallback: calculate PnL from positions when balance API fails
                current_price = all_price_data.get("PAXG_USDT", {}).get("current_price", 0)
                for pos in futures_positions:
                    hold_vol = float(pos.get('holdVol', 0))
                    if hold_vol > 0:
                        fallback_pnl = self.calculate_fallback_pnl(pos, current_price)
                        total_unrealized_pnl += fallback_pnl
                logging.info(f"Using fallback total PnL calculation: ${total_unrealized_pnl:.2f}")

            pnl_icon = "🔴" if total_unrealized_pnl < 0 else "🟢"

            trading_content += f"""```
📈 P&L Summary
Unrealized P&L:     {pnl_icon} ${total_unrealized_pnl:>8,.2f}
Open Positions:     {active_positions_count:>8}
Pending Orders:     {new_orders_count:>8}
```"""

            # Open Positions
            if futures_positions and active_positions_count > 0:
                trading_content += "```\n📊 Open Positions\n"
                for pos in futures_positions[:3]:  # Show max 3 positions
                    hold_vol = float(pos.get('holdVol', 0))
                    if hold_vol > 0:
                        symbol = pos.get('symbol', 'N/A')
                        position_type = pos.get('positionType', 1)
                        side = 'LONG' if position_type == 1 else 'SHORT'
                        position_id = pos.get('positionId', 0)

                        # Convert volume from contracts to actual asset amount
                        size_contracts = hold_vol

                        # Use official MEXC mapping for volume conversion
                        if symbol == 'BTC_USDT':
                            # Real data confirmed: 8 contracts = 0.008 BTC
                            size_asset = size_contracts * 0.001
                            asset_unit = 'BTC'
                        elif symbol == 'PAXG_USDT':
                            # Real data confirmed: 1000 contracts = 1.0 PAXG
                            size_asset = size_contracts / 1000.0
                            asset_unit = 'PAXG'
                        else:
                            # Default: assume 1 contract = 0.001 of base asset
                            size_asset = size_contracts * 0.001
                            asset_unit = symbol.split('_')[0]

                        entry_price = float(pos.get('holdAvgPrice', 0))
                        leverage = pos.get('leverage', 1)

                        # Calculate unrealized PnL with fallback
                        unrealized_pnl = 0.0
                        if futures_balance:
                            for asset in futures_balance:
                                if asset.get('currency') == 'USDT':
                                    unrealized_pnl = float(asset.get('unrealized', 0))
                                    break

                        # If balance API failed, use fallback calculation
                        if unrealized_pnl == 0.0 and futures_balance is None:
                            current_price = all_price_data.get("PAXG_USDT", {}).get("current_price", 0)
                            unrealized_pnl = self.calculate_fallback_pnl(pos, current_price)
                            logging.info(f"Using fallback PnL calculation: ${unrealized_pnl:.4f}")

                        pnl_icon = "🔴" if unrealized_pnl < 0 else "🟢"

                        # Format position size
                        size_format = f"{size_asset:.1f}" if size_asset == int(size_asset) else f"{size_asset:.4f}"

                        # Clean symbol name (remove _USDT)
                        clean_symbol = symbol.replace('_USDT', '')

                        trading_content += f"{clean_symbol} {side} {pnl_icon} ${unrealized_pnl:.4f}\n"
                        trading_content += f"Entry: ${entry_price:.2f} | Size: {size_format} {asset_unit}\n"

                        # Find all TP and SL orders for this position
                        tp_orders = []
                        sl_orders = []

                        if futures_open_orders:
                            for order in futures_open_orders:
                                order_position_id = order.get('positionId', 0)
                                if order_position_id == position_id:
                                    price = float(order.get('price', 0))
                                    vol_contracts = float(order.get('vol', 0))

                                    # Convert order volume using same logic as position
                                    if symbol == 'BTC_USDT':
                                        vol_asset = vol_contracts * 0.001
                                    elif symbol == 'PAXG_USDT':
                                        vol_asset = vol_contracts / 1000.0
                                    else:
                                        vol_asset = vol_contracts * 0.001

                                    # Determine if it's TP or SL based on price vs entry and position side
                                    if side == 'LONG':
                                        # For LONG position: TP price > entry, SL price < entry
                                        if price > entry_price:
                                            tp_orders.append({'price': price, 'vol': vol_asset})
                                        elif price < entry_price:
                                            sl_orders.append({'price': price, 'vol': vol_asset})
                                    elif side == 'SHORT':
                                        # For SHORT position: TP price < entry, SL price > entry
                                        if price < entry_price:
                                            tp_orders.append({'price': price, 'vol': vol_asset})
                                        elif price > entry_price:
                                            sl_orders.append({'price': price, 'vol': vol_asset})

                        # Display all TP orders
                        if tp_orders:
                            for i, tp in enumerate(tp_orders):
                                tp_vol_format = f"{tp['vol']:.1f}" if tp['vol'] == int(tp['vol']) else f"{tp['vol']:.4f}"
                                if len(tp_orders) == 1:
                                    trading_content += f" TP: ${tp['price']:.2f} | Vol: {tp_vol_format} {asset_unit}\n"
                                else:
                                    trading_content += f" TP{i+1}: ${tp['price']:.2f} | Vol: {tp_vol_format} {asset_unit}\n"
                        else:
                            trading_content += f" TP: None\n"

                        # Display all SL orders
                        if sl_orders:
                            for i, sl in enumerate(sl_orders):
                                sl_vol_format = f"{sl['vol']:.1f}" if sl['vol'] == int(sl['vol']) else f"{sl['vol']:.4f}"
                                if len(sl_orders) == 1:
                                    trading_content += f" SL: ${sl['price']:.2f} | Vol: {sl_vol_format} {asset_unit}\n"
                                else:
                                    trading_content += f" SL{i+1}: ${sl['price']:.2f} | Vol: {sl_vol_format} {asset_unit}\n"
                        else:
                            trading_content += f" SL: None\n"

                        trading_content += "\n"
                trading_content += "```"
            else:
                # Check if we had positions in cache (API might have failed)
                if self.last_positions_cache and any(float(pos.get('holdVol', 0)) > 0 for pos in self.last_positions_cache):
                    trading_content += "```\n📊 Open Positions\n⚠️  Position data temporarily unavailable\n(API connection issue)\n```"
                else:
                    trading_content += "```\n📊 Open Positions\nNo open positions.\n```"

            # Pending Orders - Only show new orders (not TP/SL)
            new_orders = []
            if futures_open_orders:
                for order in futures_open_orders:
                    position_id = order.get('positionId', 0)
                    # Only include orders that don't belong to any position (new orders)
                    if position_id == 0:
                        new_orders.append(order)

            if new_orders:
                trading_content += "```\n📋 Pending Orders\n"
                for order in new_orders[:3]:  # Show max 3 orders
                    symbol = order.get('symbol', 'N/A')
                    side_code = order.get('side', 1)
                    order_type_code = order.get('orderType', 1)

                    # Determine order side
                    if side_code == 1:
                        order_label = "LONG"
                    elif side_code == 2:
                        order_label = "SHORT"
                    elif side_code == 3:
                        order_label = "SHORT"  # Side code 3 is also SHORT
                    else:
                        order_label = "UNKNOWN"

                    order_type = 'LIMIT' if order_type_code == 1 else 'MARKET'
                    price = float(order.get('price', 0))
                    volume_contracts = float(order.get('vol', 0))

                    # Convert volume from contracts to actual asset amount
                    if symbol == 'BTC_USDT':
                        volume_asset = volume_contracts * 0.001
                        asset_unit = 'BTC'
                    elif symbol == 'PAXG_USDT':
                        volume_asset = volume_contracts / 1000.0
                        asset_unit = 'PAXG'
                    else:
                        volume_asset = volume_contracts * 0.001
                        asset_unit = symbol.split('_')[0]

                    # Format volume: use .1f for whole numbers, .4f for decimals
                    if volume_asset == int(volume_asset):
                        vol_format = f"{volume_asset:.1f}"
                    else:
                        vol_format = f"{volume_asset:.4f}"

                    # Clean symbol name (remove _USDT)
                    clean_symbol = symbol.replace('_USDT', '')

                    trading_content += f"{clean_symbol} {order_label} {order_type}\n"
                    trading_content += f"Price: ${price:.2f} | Vol: {vol_format} {asset_unit}\n\n"
                trading_content += "```"
            else:
                trading_content += "```\n📋 Pending Orders\nNo new position orders.\n```"

            # Store current prices for comparison and update successful timestamp
            for symbol, price_data in all_price_data.items():
                self.last_prices[symbol] = price_data['current_price']
                self.last_volumes[symbol] = price_data['volume']
            self.last_successful_update = datetime.now()

            # Combine all content
            full_content = price_content + trading_content
            embed.description = full_content
            embed.set_footer(text="MEXC Exchange • Cập nhật mỗi 60 giây")

            return embed


    
    @tasks.loop(seconds=60)
    async def price_monitor(self):
        try:
            async with MEXCClient() as client:
                # Monitor all trading pairs
                should_alert = False
                alert_data = None

                for symbol in self.trading_pairs.keys():
                    price_data = await client.get_price_data(symbol)
                    if not price_data:
                        continue

                    current_price = price_data['current_price']
                    current_volume = price_data['volume']

                    # Get last values for this symbol
                    last_price = self.last_prices.get(symbol, 0)
                    last_volume = self.last_volumes.get(symbol, 0)

                    # Calculate change percentages
                    price_change_percent = abs((current_price - last_price) / last_price * 100) if last_price > 0 else 0
                    volume_change_percent = abs((current_volume - last_volume) / last_volume * 100) if last_volume > 0 else 0

                    # Check for alerts
                    if price_change_percent >= self.price_threshold:
                        should_alert = True
                        alert_data = (price_data, "price", symbol)
                    elif volume_change_percent >= self.volume_threshold:
                        should_alert = True
                        alert_data = (price_data, "volume", symbol)

                    # Update last values
                    self.last_prices[symbol] = current_price
                    self.last_volumes[symbol] = current_volume

                # Send alert if needed
                if should_alert and alert_data:
                    price_data, alert_type, symbol = alert_data
                    embed = self.create_price_embed(price_data, alert_type=alert_type, symbol=symbol)
                    await self.paxg_channel.send(embed=embed)

                # Update pinned message
                if self.pinned_message:
                    combined_embed = await self.create_combined_dashboard()
                    if combined_embed:
                        await self.pinned_message.edit(embed=combined_embed)
                
        except Exception as e:
            logging.error(f"Error in price_monitor: {e}")
    
    @commands.command(name='paxg')
    async def manual_price_check(self, ctx):
        combined_embed = await self.create_combined_dashboard()
        if combined_embed:
            await ctx.send(embed=combined_embed)
        else:
            await ctx.send("❌ Không thể lấy dữ liệu từ MEXC API")
    
    @commands.command(name='threshold')
    async def set_threshold(self, ctx, price_threshold: float = None, volume_threshold: float = None):
        if ctx.author.id != int(self.config['discord']['admin_id']):
            await ctx.send("❌ Bạn không có quyền sử dụng lệnh này")
            return
        
        if price_threshold is not None:
            self.price_threshold = price_threshold
        if volume_threshold is not None:
            self.volume_threshold = volume_threshold
        
        await ctx.send(f"✅ Đã cập nhật ngưỡng cảnh báo:\n"
                      f"📊 Giá: {self.price_threshold}%\n"
                      f"📦 Volume: {self.volume_threshold}%")

def run_bot():
    bot = PAXGBot()
    bot.run(bot.config['discord']['token'])
