#!/usr/bin/env python3
"""
MEXC Futures Client - Pure Futures Trading API Only

This client exclusively handles MEXC Futures trading operations.
All Spot trading functionality has been removed to avoid confusion.

Features:
- Futures price data (from futures market)
- Futures positions management
- Futures account balance
- Futures open orders
- Retry mechanism with detailed logging

Author: Trading Bot System
Version: 3.0 (Futures Only - Unified Client)
"""

import aiohttp
import asyncio
import hashlib
import hmac
import json
import logging
import random
import time
from typing import Dict, Optional, List
import yaml

class MEXCClient:
    """
    Unified MEXC Futures Trading Client
    
    This client handles all MEXC Futures operations including:
    - Price data from futures market
    - Position management
    - Account balance
    - Order management
    """
    
    def __init__(self):
        self.futures_base_url = "https://contract.mexc.com"
        self.session = None
        self.api_key = None
        self.api_secret = None
        self.load_config()

    def load_config(self):
        """Load trading configuration from config.ymal"""
        try:
            with open('config.ymal', 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
                mexc_config = config.get('mexc', {})
                self.api_key = mexc_config.get('api_key')
                self.api_secret = mexc_config.get('api_secret')
                logging.info(f"Config loaded - API Key: {'***' + self.api_key[-4:] if self.api_key else 'None'}")
                logging.info(f"Config loaded - API Secret: {'***' + self.api_secret[-4:] if self.api_secret else 'None'}")
        except Exception as e:
            logging.error(f"Error loading config: {e}")

    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()

    def _generate_signature(self, query_string: str = None) -> str:
        """Generate signature for futures API requests"""
        if not self.api_secret:
            return ""

        timestamp = str(int(time.time() * 1000))
        # For MEXC Futures API: accessKey + timestamp + requestParam
        message = self.api_key + timestamp
        if query_string:
            message += query_string
        else:
            message += ""  # Empty string if no params

        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        return signature

    def _get_headers(self, signed: bool = False) -> Dict[str, str]:
        """Get request headers for futures API"""
        timestamp = str(int(time.time() * 1000))
        headers = {
            "ApiKey": self.api_key,
            "Request-Time": timestamp,
            "Content-Type": "application/json"
        }
        return headers

    async def _retry_api_call(self, func, *args, max_retries: int = 3, **kwargs):
        """Retry API call up to max_retries times if it fails or returns None"""
        for attempt in range(max_retries):
            try:
                result = await func(*args, **kwargs)
                if result is not None:
                    return result
                logging.warning(f"API call returned None, attempt {attempt + 1}/{max_retries}")
            except Exception as e:
                logging.error(f"API call failed on attempt {attempt + 1}/{max_retries}: {e}")
                if attempt == max_retries - 1:
                    raise
            
            if attempt < max_retries - 1:
                await asyncio.sleep(1)  # Wait 1 second before retry
        
        return None

    async def _get_price_data_internal(self, symbol: str = "PAXG_USDT") -> Optional[Dict]:
        """Internal method to get price data from MEXC Futures API"""
        await asyncio.sleep(random.uniform(1, 3))

        # MEXC Futures ticker API
        url = f"{self.futures_base_url}/api/v1/contract/ticker"
        params = {"symbol": symbol}

        async with self.session.get(url, params=params) as response:
            response_text = await response.text()
            logging.debug(f"Futures price API - Status: {response.status}, URL: {url}")
            logging.debug(f"Futures price API - Response: {response_text}")
            
            if response.status == 200:
                try:
                    data = await response.json()
                    if data.get('success') and data.get('data'):
                        return self._format_price_data(data['data'], symbol)
                    else:
                        logging.error(f"Futures price API returned no data: {data}")
                        return None
                except Exception as e:
                    logging.error(f"Error parsing futures price response: {e}")
                    return None
            else:
                logging.error(f"Error getting futures price data for {symbol}: {response.status}, Response: {response_text}")
                return None

    async def get_price_data(self, symbol: str = "PAXG_USDT") -> Optional[Dict]:
        """Get price data from MEXC Futures API with retry mechanism"""
        return await self._retry_api_call(self._get_price_data_internal, symbol)

    def _format_price_data(self, ticker_data: Dict, symbol: str = "PAXG_USDT") -> Dict:
        """Format futures price data for display"""
        def safe_float(value, default=0.0):
            try:
                return float(value) if value else default
            except (ValueError, TypeError):
                return default

        current_price = safe_float(ticker_data.get("lastPrice"))
        price_change_24h = safe_float(ticker_data.get("riseFallValue"))  # 24h change in price
        price_change_percent_24h = safe_float(ticker_data.get("riseFallRate")) * 100  # Convert to percentage
        
        # Convert volume from contracts to asset units for display
        volume_contracts = safe_float(ticker_data.get("volume24"))
        if symbol == "PAXG_USDT":
            volume_display = volume_contracts / 1000.0  # Convert contracts to PAXG
        else:
            volume_display = volume_contracts * 0.001  # Default conversion

        return {
            "symbol": symbol,
            "current_price": current_price,
            "price_change_24h": price_change_24h,
            "price_change_percent_24h": price_change_percent_24h,
            "high_price": safe_float(ticker_data.get("high24Price")),
            "low_price": safe_float(ticker_data.get("lower24Price")),
            "volume": volume_display
        }

    async def _get_futures_positions_internal(self, symbol: str = None) -> Optional[List]:
        """Internal method to get futures positions"""
        if not self.api_key or not self.api_secret:
            logging.error("API credentials missing for futures positions")
            return []

        await asyncio.sleep(random.uniform(1, 3))

        url = f"{self.futures_base_url}/api/v1/private/position/open_positions"

        data_original = {}
        if symbol:
            data_original["symbol"] = symbol

        query_string = '&'.join(f'{k}={v}' for k, v in sorted(data_original.items()))
        signature = self._generate_signature(query_string if query_string else None)

        headers = self._get_headers()
        headers["Signature"] = signature

        if query_string:
            url = f"{url}?{query_string}"

        async with self.session.get(url, headers=headers) as response:
            response_text = await response.text()
            logging.info(f"Futures positions API - Status: {response.status}, URL: {url}")
            logging.info(f"Futures positions API - Response: {response_text}")

            if response.status == 200:
                try:
                    result = await response.json()
                    logging.info(f"Futures positions API - Full result: {result}")
                    positions_data = result.get('data', []) if result.get('success') else []
                    logging.info(f"Futures positions API - Parsed data: {positions_data}")
                    return positions_data
                except Exception as e:
                    logging.error(f"Error parsing futures positions response: {e}")
                    return []
            else:
                logging.error(f"Error getting futures positions: {response.status}, Response: {response_text}")
                return None

    async def get_futures_positions(self, symbol: str = None) -> Optional[List]:
        """Get futures positions with retry mechanism"""
        result = await self._retry_api_call(self._get_futures_positions_internal, symbol)
        return result if result is not None else []

    async def _get_futures_account_assets_internal(self) -> Optional[List]:
        """Internal method to get futures account assets"""
        if not self.api_key or not self.api_secret:
            logging.error("API credentials missing for futures account assets")
            return []

        await asyncio.sleep(random.uniform(1, 3))

        url = f"{self.futures_base_url}/api/v1/private/account/assets"
        signature = self._generate_signature()

        headers = self._get_headers()
        headers["Signature"] = signature

        async with self.session.get(url, headers=headers) as response:
            response_text = await response.text()
            logging.info(f"Futures account assets API - Status: {response.status}, URL: {url}")
            logging.info(f"Futures account assets API - Response: {response_text}")

            if response.status == 200:
                try:
                    result = await response.json()
                    logging.info(f"Futures account assets API - Full result: {result}")
                    assets_data = result.get('data', []) if result.get('success') else []
                    logging.info(f"Futures account assets API - Parsed data: {assets_data}")
                    return assets_data
                except Exception as e:
                    logging.error(f"Error parsing futures account assets response: {e}")
                    return []
            else:
                logging.error(f"Error getting futures account assets: {response.status}, Response: {response_text}")
                return None

    async def get_futures_account_assets(self) -> Optional[List]:
        """Get futures account assets with retry mechanism"""
        result = await self._retry_api_call(self._get_futures_account_assets_internal)
        return result if result is not None else []

    async def _get_futures_open_orders_internal(self, symbol: str = None) -> Optional[List]:
        """Internal method to get futures open orders"""
        if not self.api_key or not self.api_secret:
            logging.error("API credentials missing for futures open orders")
            return []

        await asyncio.sleep(random.uniform(1, 3))

        url = f"{self.futures_base_url}/api/v1/private/order/list/open_orders"

        data_original = {}
        if symbol:
            data_original["symbol"] = symbol

        query_string = '&'.join(f'{k}={v}' for k, v in sorted(data_original.items()))
        signature = self._generate_signature(query_string if query_string else None)

        headers = self._get_headers()
        headers["Signature"] = signature

        if query_string:
            url = f"{url}?{query_string}"

        async with self.session.get(url, headers=headers) as response:
            response_text = await response.text()
            logging.debug(f"Futures open orders API - Status: {response.status}, URL: {url}")
            logging.debug(f"Futures open orders API - Response: {response_text}")

            if response.status == 200:
                try:
                    result = await response.json()
                    orders_data = result.get('data', []) if result.get('success') else []
                    logging.debug(f"Futures open orders API - Parsed data: {len(orders_data) if orders_data else 0} orders")
                    return orders_data
                except Exception as e:
                    logging.error(f"Error parsing futures open orders response: {e}")
                    return []
            else:
                logging.error(f"Error getting futures open orders: {response.status}, Response: {response_text}")
                return None

    async def get_futures_open_orders(self, symbol: str = None) -> Optional[List]:
        """Get futures open orders with retry mechanism"""
        result = await self._retry_api_call(self._get_futures_open_orders_internal, symbol)
        return result if result is not None else []
