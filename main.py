#!/usr/bin/env python3
"""
MEXC Futures Trading Bot - Main Entry Point

Optimized version focusing exclusively on futures trading operations.
All spot trading functionality has been removed for better performance.

Features:
- Real-time PAXG price monitoring
- Futures positions tracking
- Account balance monitoring
- Trading dashboard updates
- Discord integration

Author: Trading Bot System
Version: 2.0 (Futures Only)
"""

import asyncio
import logging
import sys
from discord_bot import run_bot

def main():
    """Main entry point for the futures trading bot"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('bot.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

    try:
        print("🚀 Starting MEXC Futures Trading Bot v2.0")
        print("📊 Futures-only optimized version")
        run_bot()
    except KeyboardInterrupt:
        print("\n⏹️ Bot stopped by user")
    except Exception as e:
        logging.error(f"❌ Bot error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
